import matplotlib.pyplot as plt
import mplfinance as mpf
import pandas as pd
import matplotlib.dates as mdates
import numpy as np
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import streamlit as st

def plot_candles_with_patterns_plotly(df, patterns, support_levels=None, resistance_levels=None, selected_indicators=None):
    """Create interactive candlestick chart with patterns and support/resistance using Plotly"""

    # Ensure we have the required columns
    if 'timestamp' in df.columns:
        df = df.set_index('timestamp')
    elif 'datetime' in df.columns:
        df = df.set_index(pd.to_datetime(df['datetime']))

    # Default indicators if none selected
    if selected_indicators is None:
        selected_indicators = ['RSI', 'MACD', 'VWAP']

    # Create subplots - adjust based on indicators
    has_oscillators = any(ind in selected_indicators for ind in ['RSI', 'Stoch_K', 'Stoch_D'])
    has_macd = 'MACD' in selected_indicators

    rows = 1  # Main price chart
    if has_oscillators:
        rows += 1
    if has_macd:
        rows += 1
    rows += 1  # Volume

    subplot_titles = ['Price Chart with Patterns']
    if has_oscillators:
        subplot_titles.append('Oscillators')
    if has_macd:
        subplot_titles.append('MACD')
    subplot_titles.append('Volume')

    fig = make_subplots(
        rows=rows, cols=1,
        shared_xaxes=True,
        vertical_spacing=0.05,
        subplot_titles=subplot_titles,
        row_heights=[0.6] + [0.15] * (rows - 2) + [0.25]  # Main chart larger
    )

    # Add candlestick chart
    fig.add_trace(
        go.Candlestick(
            x=df.index,
            open=df['open'],
            high=df['high'],
            low=df['low'],
            close=df['close'],
            name="OHLC",
            increasing_line_color='green',
            decreasing_line_color='red'
        ),
        row=1, col=1
    )

    # Add selected indicators to main chart
    current_row = 1

    # Add moving averages and trend lines to main chart
    trend_indicators = ['EMA_12', 'EMA_26', 'SMA_20', 'SMA_50', 'VWAP', 'BB_upper', 'BB_lower']
    for indicator in selected_indicators:
        if indicator in trend_indicators and indicator in df.columns:
            color_map = {
                'EMA_12': 'blue', 'EMA_26': 'orange', 'SMA_20': 'purple', 'SMA_50': 'brown',
                'VWAP': 'yellow', 'BB_upper': 'gray', 'BB_lower': 'gray'
            }
            fig.add_trace(
                go.Scatter(
                    x=df.index,
                    y=df[indicator],
                    mode='lines',
                    name=indicator,
                    line=dict(color=color_map.get(indicator, 'black'), width=1),
                    opacity=0.7
                ),
                row=1, col=1
            )

    current_row += 1

    # Add oscillators subplot
    if has_oscillators:
        for indicator in selected_indicators:
            if indicator in ['RSI', 'Stoch_K', 'Stoch_D'] and indicator in df.columns:
                color_map = {'RSI': 'purple', 'Stoch_K': 'blue', 'Stoch_D': 'red'}
                fig.add_trace(
                    go.Scatter(
                        x=df.index,
                        y=df[indicator],
                        mode='lines',
                        name=indicator,
                        line=dict(color=color_map.get(indicator, 'black'))
                    ),
                    row=current_row, col=1
                )

        # Add RSI overbought/oversold lines
        if 'RSI' in available_indicators:
            fig.add_hline(y=70, line_dash="dash", line_color="red", opacity=0.5, row=current_row, col=1)
            fig.add_hline(y=30, line_dash="dash", line_color="green", opacity=0.5, row=current_row, col=1)

        current_row += 1

    # Add MACD subplot
    if has_macd:
        if 'MACD' in df.columns:
            fig.add_trace(
                go.Scatter(
                    x=df.index,
                    y=df['MACD'],
                    mode='lines',
                    name='MACD',
                    line=dict(color='blue')
                ),
                row=current_row, col=1
            )

        if 'MACD_signal' in df.columns:
            fig.add_trace(
                go.Scatter(
                    x=df.index,
                    y=df['MACD_signal'],
                    mode='lines',
                    name='MACD Signal',
                    line=dict(color='red')
                ),
                row=current_row, col=1
            )

        if 'MACD_histogram' in df.columns:
            colors = ['green' if val >= 0 else 'red' for val in df['MACD_histogram']]
            fig.add_trace(
                go.Bar(
                    x=df.index,
                    y=df['MACD_histogram'],
                    name='MACD Histogram',
                    marker_color=colors,
                    opacity=0.6
                ),
                row=current_row, col=1
            )

        current_row += 1

    # Add volume bars
    colors = ['green' if close >= open else 'red'
              for close, open in zip(df['close'], df['open'])]

    fig.add_trace(
        go.Bar(
            x=df.index,
            y=df['volume'],
            name="Volume",
            marker_color=colors,
            opacity=0.6
        ),
        row=current_row, col=1
    )

    # Add support levels
    if support_levels:
        for i, level in enumerate(support_levels):
            fig.add_hline(
                y=level,
                line_dash="dash",
                line_color="green",
                annotation_text=f"Support {i+1}: {level:.2f}",
                annotation_position="bottom right",
                row=1, col=1
            )

    # Add resistance levels
    if resistance_levels:
        for i, level in enumerate(resistance_levels):
            fig.add_hline(
                y=level,
                line_dash="dash",
                line_color="red",
                annotation_text=f"Resistance {i+1}: {level:.2f}",
                annotation_position="top right",
                row=1, col=1
            )

    # Add pattern annotations
    if patterns:
        for idx, pattern_name in patterns[-10:]:  # Show last 10 patterns
            if idx < len(df):
                fig.add_annotation(
                    x=df.index[idx],
                    y=df['high'].iloc[idx],
                    text=pattern_name,
                    showarrow=True,
                    arrowhead=2,
                    arrowsize=1,
                    arrowwidth=2,
                    arrowcolor="blue",
                    ax=0,
                    ay=-30,
                    bgcolor="rgba(255,255,255,0.8)",
                    bordercolor="blue",
                    borderwidth=1,
                    font=dict(size=10, color="blue"),
                    row=1, col=1
                )

    # Update layout
    fig.update_layout(
        title="Live Market Analysis with Patterns & Levels",
        height=700,  # Increased height for better visibility
        showlegend=True,
        xaxis_rangeslider_visible=False,
        margin=dict(l=50, r=50, t=80, b=50),
        plot_bgcolor='rgba(0,0,0,0)',
        paper_bgcolor='rgba(0,0,0,0)'
    )

    # Update x-axis for all subplots
    fig.update_xaxes(showgrid=True, gridwidth=1, gridcolor='lightgray')
    fig.update_yaxes(showgrid=True, gridwidth=1, gridcolor='lightgray')

    # Set y-axis titles
    fig.update_yaxes(title_text="Price", row=1, col=1)
    if has_oscillators:
        fig.update_yaxes(title_text="Oscillators", row=2, col=1)
    if has_macd:
        macd_row = 3 if has_oscillators else 2
        fig.update_yaxes(title_text="MACD", row=macd_row, col=1)

    volume_row = rows
    fig.update_yaxes(title_text="Volume", row=volume_row, col=1)
    fig.update_xaxes(title_text="Time", row=volume_row, col=1)

    return fig

def plot_candles_with_patterns(df, patterns, support_levels=None, resistance_levels=None):
    """Create matplotlib candlestick chart with patterns and support/resistance (fallback)"""

    # Ensure datetime is the index and in the correct format
    if 'timestamp' in df.columns:
        df = df.set_index('timestamp')
    elif 'datetime' in df.columns:
        df = df.set_index(pd.to_datetime(df['datetime']))

    # Create a list to hold pattern annotations
    annotations = []

    # Add pattern markers
    for idx, pattern_name in patterns[-10:]:  # Show last 10 patterns
        if idx < len(df):
            annotations.append(
                mpf.make_addplot(
                    [df['high'].iloc[idx]],
                    type='scatter',
                    marker='^',
                    markersize=100,
                    color='blue'
                )
            )

    # Add support/resistance lines
    if support_levels:
        for level in support_levels:
            support_line = [level] * len(df)
            annotations.append(
                mpf.make_addplot(
                    support_line,
                    type='line',
                    color='green',
                    linestyle='--',
                    alpha=0.7
                )
            )

    if resistance_levels:
        for level in resistance_levels:
            resistance_line = [level] * len(df)
            annotations.append(
                mpf.make_addplot(
                    resistance_line,
                    type='line',
                    color='red',
                    linestyle='--',
                    alpha=0.7
                )
            )

    # Define the plot style
    style = mpf.make_mpf_style(
        base_mpf_style='yahoo',
        rc={'figure.figsize': (14, 10)}
    )

    # Create the plot
    fig, axes = mpf.plot(
        df,
        type='candle',
        style=style,
        volume=True,
        addplot=annotations if annotations else None,
        returnfig=True,
        title="Market Analysis with Patterns & Levels"
    )

    # Add pattern labels
    if len(axes) > 0:
        ax = axes[0]
        for idx, pattern_name in patterns[-10:]:  # Show last 10 patterns
            if idx < len(df):
                candle_time = df.index[idx]
                ax.annotate(
                    pattern_name,
                    (mdates.date2num(candle_time), df['high'].iloc[idx]),
                    xytext=(0, 10),
                    textcoords='offset points',
                    ha='center',
                    va='bottom',
                    color='blue',
                    fontsize=8,
                    rotation=45,
                    bbox=dict(boxstyle="round,pad=0.3", facecolor="white", alpha=0.8)
                )

    return fig

def create_live_chart_placeholder():
    """Create a placeholder chart for initial load"""
    fig = go.Figure()
    fig.add_annotation(
        text="Waiting for live data...",
        xref="paper", yref="paper",
        x=0.5, y=0.5,
        showarrow=False,
        font=dict(size=20)
    )
    fig.update_layout(
        title="Live Market Dashboard",
        height=700,  # Match the main chart height
        showlegend=False,
        margin=dict(l=50, r=50, t=80, b=50)
    )
    return fig

def create_professional_candlestick_chart(df, patterns, support_levels=None, resistance_levels=None, selected_indicators=None, ml_prediction=None):
    """Create a Zerodha-style candlestick chart optimized for real-time updates with ML predictions"""

    # Ensure we have the required columns
    if 'timestamp' in df.columns:
        df = df.set_index('timestamp')
    elif 'datetime' in df.columns:
        df = df.set_index(pd.to_datetime(df['datetime']))

    # Default indicators if none selected
    if selected_indicators is None:
        selected_indicators = ['VWAP']  # Minimal by default

    # Filter selected indicators to only include available ones
    available_indicators = [ind for ind in selected_indicators if ind in df.columns]

    # Create subplots - minimal layout like Zerodha
    has_volume = True  # Always show volume like Zerodha
    has_oscillators = any(ind in available_indicators for ind in ['RSI', 'Stoch_K', 'Stoch_D'])
    has_macd = 'MACD' in available_indicators

    rows = 1  # Main price chart
    if has_oscillators:
        rows += 1
    if has_macd:
        rows += 1
    if has_volume:
        rows += 1

    # Minimal titles like Zerodha
    subplot_titles = ['']  # No title for main chart
    if has_oscillators:
        subplot_titles.append('')
    if has_macd:
        subplot_titles.append('')
    if has_volume:
        subplot_titles.append('')

    fig = make_subplots(
        rows=rows, cols=1,
        shared_xaxes=True,
        vertical_spacing=0.02,  # Tighter spacing like Zerodha
        subplot_titles=subplot_titles,
        row_heights=[0.7] + [0.1] * (rows - 2) + [0.2] if has_volume else [1.0]  # Main chart dominates
    )

    # Add Zerodha-style candlestick chart
    fig.add_trace(
        go.Candlestick(
            x=df.index,
            open=df['open'],
            high=df['high'],
            low=df['low'],
            close=df['close'],
            name="",  # No name for cleaner look
            increasing_line_color='#00C851',  # Zerodha green
            decreasing_line_color='#FF4444',  # Zerodha red
            increasing_fillcolor='#00C851',
            decreasing_fillcolor='#FF4444',
            line=dict(width=0.8),
            increasing_line_width=0.8,
            decreasing_line_width=0.8,
            showlegend=False  # Clean like Zerodha
        ),
        row=1, col=1
    )

    current_row = 1

    # Add ML prediction visualization
    if ml_prediction and not df.empty:
        latest_price = df['close'].iloc[-1]
        latest_time = df.index[-1]

        # Add prediction arrow and signal
        if 'CE' in ml_prediction.get('signal', ''):
            # Bullish prediction
            fig.add_annotation(
                x=latest_time,
                y=latest_price,
                text="🔥 BUY CE",
                showarrow=True,
                arrowhead=2,
                arrowsize=1.5,
                arrowwidth=3,
                arrowcolor="#00C851",
                ax=0,
                ay=-40,
                bgcolor="rgba(0, 200, 81, 0.9)",
                bordercolor="#00C851",
                borderwidth=2,
                font=dict(size=12, color="white", family="Arial Black"),
                row=1, col=1
            )
        elif 'PE' in ml_prediction.get('signal', ''):
            # Bearish prediction
            fig.add_annotation(
                x=latest_time,
                y=latest_price,
                text="🔥 BUY PE",
                showarrow=True,
                arrowhead=2,
                arrowsize=1.5,
                arrowwidth=3,
                arrowcolor="#FF4444",
                ax=0,
                ay=40,
                bgcolor="rgba(255, 68, 68, 0.9)",
                bordercolor="#FF4444",
                borderwidth=2,
                font=dict(size=12, color="white", family="Arial Black"),
                row=1, col=1
            )

    # Add minimal indicators to main chart (Zerodha style)
    trend_indicators = ['VWAP', 'EMA_12', 'EMA_26', 'SMA_20', 'SMA_50']
    indicator_colors = {
        'VWAP': '#FF9500',  # Orange like Zerodha
        'EMA_12': '#2196F3', 'EMA_26': '#9C27B0',
        'SMA_20': '#4CAF50', 'SMA_50': '#F44336'
    }

    for indicator in available_indicators:
        if indicator in trend_indicators and indicator in df.columns:
            fig.add_trace(
                go.Scatter(
                    x=df.index,
                    y=df[indicator],
                    mode='lines',
                    name=indicator,
                    line=dict(color=indicator_colors.get(indicator, '#17becf'), width=1.2),
                    opacity=0.8,
                    showlegend=False  # Clean like Zerodha
                ),
                row=1, col=1
            )



    # Add support/resistance levels (minimal Zerodha style)
    if support_levels:
        for i, level in enumerate(support_levels[:2]):  # Show only top 2 levels
            fig.add_hline(
                y=level,
                line_dash="dot",
                line_color="#00C851",
                line_width=1,
                opacity=0.5,
                annotation_text=f"S{level:.0f}",
                annotation_position="left",
                annotation_font_size=9,
                annotation_font_color="#00C851",
                row=1, col=1
            )

    if resistance_levels:
        for i, level in enumerate(resistance_levels[:2]):  # Show only top 2 levels
            fig.add_hline(
                y=level,
                line_dash="dot",
                line_color="#FF4444",
                line_width=1,
                opacity=0.5,
                annotation_text=f"R{level:.0f}",
                annotation_position="left",
                annotation_font_size=9,
                annotation_font_color="#FF4444",
                row=1, col=1
            )

    # Add ML prediction levels if available
    if ml_prediction and ml_prediction.get('stoploss', 0) > 0:
        current_price = df['close'].iloc[-1]
        sl_price = ml_prediction['stoploss']
        target_price = ml_prediction['target']

        # Stop loss line
        fig.add_hline(
            y=sl_price,
            line_dash="solid",
            line_color="#FF4444",
            line_width=2,
            opacity=0.8,
            annotation_text=f"SL: {sl_price:.0f}",
            annotation_position="right",
            annotation_font_size=10,
            annotation_font_color="#FF4444",
            row=1, col=1
        )

        # Target line
        fig.add_hline(
            y=target_price,
            line_dash="solid",
            line_color="#00C851",
            line_width=2,
            opacity=0.8,
            annotation_text=f"TGT: {target_price:.0f}",
            annotation_position="right",
            annotation_font_size=10,
            annotation_font_color="#00C851",
            row=1, col=1
        )

    current_row += 1

    # Add oscillators subplot
    if has_oscillators:
        oscillator_colors = {'RSI': '#9467bd', 'Stoch_K': '#1f77b4', 'Stoch_D': '#ff7f0e'}
        for indicator in available_indicators:  # Use available_indicators
            if indicator in ['RSI', 'Stoch_K', 'Stoch_D'] and indicator in df.columns:
                fig.add_trace(
                    go.Scatter(
                        x=df.index,
                        y=df[indicator],
                        mode='lines',
                        name=indicator,
                        line=dict(color=oscillator_colors.get(indicator, '#17becf'), width=1.5)
                    ),
                    row=current_row, col=1
                )

        # Add RSI overbought/oversold lines
        if 'RSI' in selected_indicators:
            fig.add_hline(y=70, line_dash="dot", line_color="#ff4444", opacity=0.5, row=current_row, col=1)
            fig.add_hline(y=30, line_dash="dot", line_color="#00ff88", opacity=0.5, row=current_row, col=1)
            fig.add_hline(y=50, line_dash="dot", line_color="#888888", opacity=0.3, row=current_row, col=1)

        current_row += 1

    # Add MACD subplot
    if has_macd:
        if 'MACD' in df.columns:
            fig.add_trace(
                go.Scatter(
                    x=df.index,
                    y=df['MACD'],
                    mode='lines',
                    name='MACD',
                    line=dict(color='#1f77b4', width=1.5)
                ),
                row=current_row, col=1
            )

        if 'MACD_signal' in df.columns:
            fig.add_trace(
                go.Scatter(
                    x=df.index,
                    y=df['MACD_signal'],
                    mode='lines',
                    name='Signal',
                    line=dict(color='#ff7f0e', width=1.5)
                ),
                row=current_row, col=1
            )

        if 'MACD_histogram' in df.columns:
            colors = ['#00ff88' if val >= 0 else '#ff4444' for val in df['MACD_histogram']]
            fig.add_trace(
                go.Bar(
                    x=df.index,
                    y=df['MACD_histogram'],
                    name='Histogram',
                    marker_color=colors,
                    opacity=0.7
                ),
                row=current_row, col=1
            )

        current_row += 1

    # Add volume bars (Zerodha style)
    if has_volume:
        volume_colors = ['#00C851' if close >= open else '#FF4444'
                        for close, open in zip(df['close'], df['open'])]

        fig.add_trace(
            go.Bar(
                x=df.index,
                y=df['volume'],
                name="",
                marker_color=volume_colors,
                opacity=0.7,
                showlegend=False
            ),
            row=current_row, col=1
        )

    # Add pattern annotations
    if patterns:
        for idx, pattern_name in patterns[-5:]:  # Show last 5 patterns
            if idx < len(df):
                fig.add_annotation(
                    x=df.index[idx],
                    y=df['high'].iloc[idx],
                    text=pattern_name,
                    showarrow=True,
                    arrowhead=2,
                    arrowsize=1,
                    arrowwidth=2,
                    arrowcolor="#1f77b4",
                    ax=0,
                    ay=-30,
                    bgcolor="rgba(255,255,255,0.9)",
                    bordercolor="#1f77b4",
                    borderwidth=1,
                    font=dict(size=9, color="#1f77b4"),
                    row=1, col=1
                )

    # Zerodha-style layout (minimal and clean)
    fig.update_layout(
        title=None,  # No title like Zerodha
        height=700,
        showlegend=False,  # Clean interface
        xaxis_rangeslider_visible=False,
        margin=dict(l=50, r=50, t=20, b=40),  # Minimal margins
        plot_bgcolor='#FFFFFF',  # Pure white like Zerodha
        paper_bgcolor='#FFFFFF',
        font=dict(family="Arial", size=10, color="#333333"),
        dragmode='pan',  # Enable panning like Zerodha
        hovermode='x unified'  # Better hover like Zerodha
    )

    # Zerodha-style axes
    fig.update_xaxes(
        showgrid=True,
        gridwidth=0.3,
        gridcolor='#F0F0F0',
        showline=False,
        tickfont=dict(size=10, color='#666666'),
        tickformat='%H:%M',
        dtick=300000  # 5-minute intervals
    )
    fig.update_yaxes(
        showgrid=True,
        gridwidth=0.3,
        gridcolor='#F0F0F0',
        showline=False,
        tickfont=dict(size=10, color='#666666'),
        tickformat=',.0f',  # No decimals for cleaner look
        side='right'  # Price on right like Zerodha
    )

    # Minimal axis titles (Zerodha style)
    fig.update_yaxes(
        title_text="",  # No title for cleaner look
        row=1, col=1,
        tickfont=dict(size=10, color='#333333')
    )

    if has_volume:
        volume_row = rows
        fig.update_yaxes(title_text="", row=volume_row, col=1)
        fig.update_xaxes(
            title_text="",  # No title
            row=volume_row, col=1,
            tickfont=dict(size=10, color='#666666'),
            tickformat='%H:%M'
        )

    return fig

def create_zerodha_style_chart(df, patterns=None, support_levels=None, resistance_levels=None, ml_prediction=None, show_indicators=True):
    """Create a chart that closely mimics Zerodha Kite's real-time candlestick chart"""

    # Ensure we have the required columns
    if 'timestamp' in df.columns:
        df = df.set_index('timestamp')
    elif 'datetime' in df.columns:
        df = df.set_index(pd.to_datetime(df['datetime']))

    # Create main chart with volume subplot (like Zerodha)
    fig = make_subplots(
        rows=2, cols=1,
        shared_xaxes=True,
        vertical_spacing=0.02,
        subplot_titles=['', ''],
        row_heights=[0.8, 0.2]  # Main chart 80%, volume 20%
    )

    # Add candlestick chart with Zerodha colors
    fig.add_trace(
        go.Candlestick(
            x=df.index,
            open=df['open'],
            high=df['high'],
            low=df['low'],
            close=df['close'],
            name="",
            increasing_line_color='#00C851',
            decreasing_line_color='#FF4444',
            increasing_fillcolor='#00C851',
            decreasing_fillcolor='#FF4444',
            line=dict(width=0.8),
            showlegend=False
        ),
        row=1, col=1
    )

    # Add VWAP if available (always shown in Zerodha)
    if 'VWAP' in df.columns and show_indicators:
        fig.add_trace(
            go.Scatter(
                x=df.index,
                y=df['VWAP'],
                mode='lines',
                name="VWAP",
                line=dict(color='#FF9500', width=1.5),
                opacity=0.8,
                showlegend=False
            ),
            row=1, col=1
        )

    # Add ML prediction visualization
    if ml_prediction and not df.empty:
        latest_price = df['close'].iloc[-1]
        latest_time = df.index[-1]
        confidence = ml_prediction.get('confidence', 0)

        # Add prediction signal
        if 'CE' in ml_prediction.get('signal', ''):
            fig.add_annotation(
                x=latest_time,
                y=latest_price * 1.002,  # Slightly above price
                text=f"🚀 CE {confidence:.0f}%",
                showarrow=True,
                arrowhead=1,
                arrowsize=1,
                arrowwidth=2,
                arrowcolor="#00C851",
                ax=0,
                ay=-25,
                bgcolor="rgba(0, 200, 81, 0.9)",
                bordercolor="#00C851",
                borderwidth=1,
                font=dict(size=10, color="white"),
                row=1, col=1
            )
        elif 'PE' in ml_prediction.get('signal', ''):
            fig.add_annotation(
                x=latest_time,
                y=latest_price * 0.998,  # Slightly below price
                text=f"🔻 PE {confidence:.0f}%",
                showarrow=True,
                arrowhead=1,
                arrowsize=1,
                arrowwidth=2,
                arrowcolor="#FF4444",
                ax=0,
                ay=25,
                bgcolor="rgba(255, 68, 68, 0.9)",
                bordercolor="#FF4444",
                borderwidth=1,
                font=dict(size=10, color="white"),
                row=1, col=1
            )

    # Add volume
    volume_colors = ['#00C851' if close >= open else '#FF4444'
                    for close, open in zip(df['close'], df['open'])]

    fig.add_trace(
        go.Bar(
            x=df.index,
            y=df['volume'],
            name="",
            marker_color=volume_colors,
            opacity=0.6,
            showlegend=False
        ),
        row=2, col=1
    )

    # Zerodha-style layout
    fig.update_layout(
        title=None,
        height=600,
        showlegend=False,
        xaxis_rangeslider_visible=False,
        margin=dict(l=40, r=40, t=10, b=30),
        plot_bgcolor='#FFFFFF',
        paper_bgcolor='#FFFFFF',
        font=dict(family="Arial", size=9, color="#333333"),
        dragmode='pan',
        hovermode='x unified'
    )

    # Clean axes
    fig.update_xaxes(
        showgrid=True,
        gridwidth=0.3,
        gridcolor='#F5F5F5',
        showline=False,
        tickfont=dict(size=9, color='#666666'),
        tickformat='%H:%M'
    )
    fig.update_yaxes(
        showgrid=True,
        gridwidth=0.3,
        gridcolor='#F5F5F5',
        showline=False,
        tickfont=dict(size=9, color='#666666'),
        tickformat=',.0f',
        side='right'
    )

    return fig