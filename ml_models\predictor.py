import joblib
import pandas as pd
from pattern_engine import detect_patterns, calculate_support_resistance
from utils.indicators import detect_trend

# Load models with error handling (prioritize most accurate model)
try:
    # Try to load advanced catboost model first (highest accuracy: 87.8%)
    model = joblib.load("ml_models/advanced_catboost_classifier.pkl")
    scaler = joblib.load("ml_models/advanced_catboost_scaler.pkl")
    model_type = "advanced_catboost"
    print("🎯 Loaded ADVANCED CATBOOST ML model (87.8% accuracy)")
except Exception as e:
    print(f"⚠️ Could not load advanced catboost model: {e}")
    try:
        # Try to load real data model second
        model = joblib.load("ml_models/real_data_classifier.pkl")
        scaler = joblib.load("ml_models/real_data_scaler.pkl")
        model_type = "real_data"
        print("🎯 Loaded REAL DATA ML model (90-day trained)")
    except Exception as e:
        print(f"⚠️ Could not load real data model: {e}")
        try:
            # Try improved model
            model = joblib.load("ml_models/improved_cepe_classifier.pkl")
            scaler = joblib.load("ml_models/feature_scaler.pkl")
            model_type = "improved"
            print("✅ Loaded improved ML model")
        except Exception as e:
            print(f"⚠️ Could not load improved model: {e}")
            try:
                # Basic model fallback
                model = joblib.load("ml_models/cepe_classifier.pkl")
                scaler = None
                model_type = "basic"
                print("⚠️ Using basic model")
            except Exception as e:
                print(f"❌ Could not load any ML models: {e}")
                print("📊 Using fallback logic for predictions.")
                model = None
                scaler = None
                model_type = "fallback"

def predict_ce_pe(df):
    """Enhanced CE/PE prediction with multiple factors"""
    try:
        if df is None or len(df) < 10:
            return {"signal": "Unknown", "confidence": 0, "stoploss": 0, "target": 0, "patterns": [], "trend": "Unknown", "risk_reward_ratio": "1:2"}

        # Calculate indicators if not present
        required_cols = ["MACD", "RSI", "VWAP"]
        if not all(col in df.columns for col in required_cols):
            from utils.indicators import calculate_indicators
            df = calculate_indicators(df)

        # Get latest values
        latest_data = df.dropna().tail(5)
        if len(latest_data) == 0:
            return {"signal": "Unknown", "confidence": 0, "stoploss": 0, "target": 0, "patterns": [], "trend": "Unknown", "risk_reward_ratio": "1:2"}

        # ML Model Prediction
        if model is not None:
            try:
                if model_type in ["advanced_catboost", "real_data"]:
                    # Use advanced model with comprehensive feature engineering
                    features_df = latest_data[required_cols].iloc[-1:].copy()

                    # Base technical features
                    features_df['rsi_oversold'] = (features_df['RSI'] < 30).astype(int)
                    features_df['rsi_overbought'] = (features_df['RSI'] > 70).astype(int)
                    features_df['rsi_momentum'] = latest_data['RSI'].diff().iloc[-1] if len(latest_data) > 1 else 0
                    features_df['macd_signal'] = (features_df['MACD'] > 0).astype(int)
                    features_df['macd_momentum'] = latest_data['MACD'].diff().iloc[-1] if len(latest_data) > 1 else 0
                    features_df['price_vs_vwap'] = (latest_data['close'].iloc[-1] > features_df['VWAP']).astype(int)
                    features_df['vwap_distance'] = (latest_data['close'].iloc[-1] - features_df['VWAP']) / features_df['VWAP']
                    features_df['price_range'] = (latest_data['high'].iloc[-1] - latest_data['low'].iloc[-1]) / latest_data['close'].iloc[-1]

                    # Volume features
                    volume_ma = latest_data['volume'].rolling(20).mean().iloc[-1] if len(latest_data) >= 20 else latest_data['volume'].mean()
                    features_df['volume_ratio'] = latest_data['volume'].iloc[-1] / volume_ma if volume_ma > 0 else 1.0
                    features_df['volume_momentum'] = latest_data['volume'].pct_change().iloc[-1] if len(latest_data) > 1 else 0

                    # Enhanced volatility features
                    returns = latest_data['close'].pct_change()
                    features_df['volatility_5'] = returns.rolling(5).std().iloc[-1] if len(latest_data) >= 5 else returns.std()
                    features_df['volatility_20'] = returns.rolling(20).std().iloc[-1] if len(latest_data) >= 20 else returns.std()
                    features_df['volatility_ratio'] = features_df['volatility_5'] / features_df['volatility_20'] if features_df['volatility_20'] > 0 else 1.0

                    # Momentum features
                    features_df['momentum_5'] = latest_data['close'].pct_change(5).iloc[-1] if len(latest_data) >= 5 else 0
                    features_df['momentum_10'] = latest_data['close'].pct_change(10).iloc[-1] if len(latest_data) >= 10 else 0
                    features_df['momentum_20'] = latest_data['close'].pct_change(20).iloc[-1] if len(latest_data) >= 20 else 0

                    # Trend features
                    sma_5 = latest_data['close'].rolling(5).mean().iloc[-1] if len(latest_data) >= 5 else latest_data['close'].iloc[-1]
                    sma_20 = latest_data['close'].rolling(20).mean().iloc[-1] if len(latest_data) >= 20 else latest_data['close'].iloc[-1]
                    sma_50 = latest_data['close'].rolling(50).mean().iloc[-1] if len(latest_data) >= 50 else latest_data['close'].iloc[-1]
                    features_df['trend_short'] = (latest_data['close'].iloc[-1] > sma_5).astype(int)
                    features_df['trend_medium'] = (latest_data['close'].iloc[-1] > sma_20).astype(int)
                    features_df['trend_long'] = (latest_data['close'].iloc[-1] > sma_50).astype(int)

                    # Price position features
                    features_df['high_low_ratio'] = latest_data['high'].iloc[-1] / latest_data['low'].iloc[-1] if latest_data['low'].iloc[-1] > 0 else 1.0
                    price_range = latest_data['high'].iloc[-1] - latest_data['low'].iloc[-1]
                    features_df['close_position'] = (latest_data['close'].iloc[-1] - latest_data['low'].iloc[-1]) / price_range if price_range > 0 else 0.5

                    # Handle NaN values intelligently
                    for col in features_df.columns:
                        if 'momentum' in col or col.endswith('_momentum'):
                            features_df[col] = features_df[col].fillna(0)
                        elif 'ratio' in col:
                            features_df[col] = features_df[col].fillna(1.0)
                        elif 'position' in col:
                            features_df[col] = features_df[col].fillna(0.5)
                        else:
                            features_df[col] = features_df[col].fillna(features_df[col].median())

                    # Advanced feature columns (matching training)
                    feature_cols = [
                        'RSI', 'MACD', 'VWAP',
                        'rsi_oversold', 'rsi_overbought', 'rsi_momentum',
                        'macd_signal', 'macd_momentum',
                        'price_vs_vwap', 'vwap_distance',
                        'price_range', 'volume_ratio',
                        'trend_short', 'trend_medium', 'trend_long',
                        'volatility_5', 'volatility_20', 'volatility_ratio',
                        'volume_momentum', 'momentum_5', 'momentum_10', 'momentum_20',
                        'high_low_ratio', 'close_position'
                    ]

                    # Filter only available features
                    available_features = [col for col in feature_cols if col in features_df.columns]
                    features_scaled = scaler.transform(features_df[available_features])

                    # Predict (0=PE, 1=Neutral, 2=CE)
                    pred = model.predict(features_scaled)[0]
                    proba = model.predict_proba(features_scaled)[0].max()

                else:
                    # Use simple ML model (RandomForest)
                    features = latest_data[required_cols].iloc[-1].values.reshape(1, -1)
                    features_scaled = scaler.transform(features)
                    pred = model.predict(features_scaled)[0]
                    proba = model.predict_proba(features_scaled)[0].max()

                # Convert prediction to signal based on model type
                if model_type in ["advanced_catboost", "real_data"]:
                    if pred == 2:
                        ml_signal = "Buy CE"
                    elif pred == 0:
                        ml_signal = "Buy PE"
                    else:
                        ml_signal = "Hold"
                else:
                    ml_signal = "Buy CE" if pred == 1 else "Buy PE"

                ml_confidence = proba

            except Exception as e:
                print(f"ML model error: {e}")
                ml_signal, ml_confidence = fallback_prediction(latest_data)
        else:
            ml_signal, ml_confidence = fallback_prediction(latest_data)

        # Pattern Analysis
        patterns = detect_patterns(df)
        recent_patterns = [p[1] for p in patterns[-5:]]  # Get the last 5 patterns

        # Support/Resistance Analysis
        support_levels, resistance_levels = calculate_support_resistance(df)

        # Trend Analysis
        trend = detect_trend(df)

        # Enhanced Signal Logic
        final_signal, final_confidence = enhance_signal_with_patterns_and_trend(
            ml_signal, ml_confidence, recent_patterns, trend, latest_data
        )

        # Calculate SL/TP with 1:2 and 1:3 risk-reward ratios
        current_price = latest_data["close"].iloc[-1]
        atr = latest_data.get("ATR", pd.Series([current_price * 0.02])).iloc[-1]  # Fallback ATR

        # Use 1:3 ratio for higher confidence signals, 1:2 for lower confidence
        risk_multiplier = 1.0  # 1x ATR for stop loss
        reward_multiplier = 3.0 if final_confidence > 0.7 else 2.0  # 1:3 for high confidence, 1:2 for medium

        if final_signal == "Buy CE":
            # For CE: SL below current price, Target above with 1:2 or 1:3 ratios
            stoploss = current_price - (risk_multiplier * atr)
            target = current_price + (reward_multiplier * atr)
        else:  # Buy PE
            # For PE: SL above current price, Target below with 1:2 or 1:3 ratios
            stoploss = current_price + (risk_multiplier * atr)
            target = current_price - (reward_multiplier * atr)

        # Use support/resistance for better SL/TP
        if support_levels and final_signal == "Buy CE":
            nearest_support = max([s for s in support_levels if s < current_price], default=stoploss)
            stoploss = max(stoploss, nearest_support)

        if resistance_levels and final_signal == "Buy PE":
            nearest_resistance = min([r for r in resistance_levels if r > current_price], default=stoploss)
            stoploss = min(stoploss, nearest_resistance)

        return {
            "signal": final_signal,
            "confidence": round(final_confidence * 100, 2),
            "stoploss": round(stoploss, 2),
            "target": round(target, 2),
            "patterns": recent_patterns,
            "trend": trend,
            "support_levels": support_levels,
            "resistance_levels": resistance_levels,
            "current_price": round(current_price, 2),
            "atr": round(atr, 2),
            "risk_reward_ratio": f"1:{int(reward_multiplier)}"
        }

    except Exception as e:
        print(f"Prediction error: {e}")
        return {
            "signal": "Unknown",
            "confidence": 0,
            "stoploss": 0,
            "target": 0,
            "patterns": [],
            "trend": "Unknown",
            "support_levels": [],
            "resistance_levels": [],
            "current_price": 0,
            "atr": 0,
            "risk_reward_ratio": "1:2"
        }

def fallback_prediction(df):
    """Enhanced fallback prediction logic when ML model is not available"""
    try:
        latest = df.iloc[-1]

        # Enhanced logic based on multiple indicators
        signals = {}

        # MACD Signal (Momentum)
        macd_signal = 1 if latest["MACD"] > 0 else 0
        signals['MACD'] = macd_signal

        # RSI Signal (Momentum/Overbought-Oversold)
        rsi_value = latest["RSI"]
        if rsi_value > 70:
            rsi_signal = 0  # Overbought, bearish
        elif rsi_value < 30:
            rsi_signal = 1  # Oversold, bullish
        else:
            rsi_signal = 1 if rsi_value > 50 else 0  # Above/below midline
        signals['RSI'] = rsi_signal

        # Price vs VWAP (Trend)
        vwap_signal = 1 if latest["close"] > latest["VWAP"] else 0
        signals['VWAP'] = vwap_signal

        # Additional signals for better accuracy
        # EMA crossover
        if 'EMA_12' in df.columns and 'EMA_26' in df.columns:
            ema_signal = 1 if latest["EMA_12"] > latest["EMA_26"] else 0
            signals['EMA'] = ema_signal

        # Volume confirmation
        if len(df) >= 20:
            avg_volume = df['volume'].tail(20).mean()
            volume_signal = 1 if latest['volume'] > avg_volume else 0
            signals['Volume'] = volume_signal

        # Combine signals with weights
        total_score = sum(signals.values())
        max_signals = len(signals)

        # Calculate confidence based on signal strength
        confidence = (total_score / max_signals) * 0.8  # Max 80% for fallback

        # Decision logic
        if total_score >= (max_signals * 0.6):  # 60% or more bullish signals
            return "Buy CE", max(0.6, confidence)
        else:
            return "Buy PE", max(0.6, confidence)

    except Exception as e:
        print(f"Fallback prediction error: {e}")
        return "Buy CE", 0.5

def enhance_signal_with_patterns_and_trend(ml_signal, ml_confidence, patterns, trend, latest_data):
    """Enhance ML signal with pattern and trend analysis"""

    confidence_adjustment = 0
    signal = ml_signal

    # Pattern-based adjustments
    bullish_patterns = ["Bullish Engulfing", "Hammer", "Piercing Pattern", "Double Bottom",
                       "Inverse Head and Shoulders", "Bull Flag", "Ascending Triangle"]
    bearish_patterns = ["Bearish Engulfing", "Shooting Star", "Dark Cloud Cover", "Double Top",
                       "Head and Shoulders", "Bear Flag", "Descending Triangle"]

    pattern_score = 0
    for pattern in patterns:
        if pattern in bullish_patterns:
            pattern_score += 0.1
        elif pattern in bearish_patterns:
            pattern_score -= 0.1

    # Trend-based adjustments
    if trend == "Bullish":
        if signal == "Buy CE":
            confidence_adjustment += 0.15
        else:
            confidence_adjustment -= 0.1
    elif trend == "Bearish":
        if signal == "Buy PE":
            confidence_adjustment += 0.15
        else:
            confidence_adjustment -= 0.1

    # Combine adjustments
    final_confidence = min(0.95, max(0.05, ml_confidence + confidence_adjustment + pattern_score))

    # Change signal if confidence drops too low
    if final_confidence < 0.3:
        signal = "Buy PE" if signal == "Buy CE" else "Buy CE"
        final_confidence = 0.6

    return signal, final_confidence