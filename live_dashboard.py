import streamlit as st
import pandas as pd
import time
import threading
from datetime import datetime, timedelta
import plotly.graph_objects as go
from plotly.subplots import make_subplots

# Import our modules
from kite_websocket import KiteTickerEngine, get_instrument_token
from pattern_engine import detect_patterns, calculate_support_resistance
from utils.indicators import calculate_indicators, detect_trend
from utils.chart_utils import plot_candles_with_patterns_plotly, create_live_chart_placeholder, create_professional_candlestick_chart, create_zerodha_style_chart
from ml_models.predictor import predict_ce_pe
from kite_auth_helper import handle_kite_auth_in_streamlit, get_kite_connection
from market_status import get_market_status_display, should_use_live_data, get_data_source_info

# Configure Streamlit page
st.set_page_config(
    page_title="🚀 Live Trading Dashboard",
    page_icon="📈",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Custom CSS for better styling
st.markdown("""
<style>
    .main-header {
        font-size: 2.5rem;
        font-weight: bold;
        text-align: center;
        color: #1f77b4;
        margin-bottom: 1rem;
    }
    .metric-card {
        background-color: #f0f2f6;
        padding: 1rem;
        border-radius: 0.5rem;
        border-left: 4px solid #1f77b4;
    }
    .signal-bullish {
        background-color: #d4edda;
        color: #155724;
        padding: 0.5rem;
        border-radius: 0.25rem;
        font-weight: bold;
    }
    .signal-bearish {
        background-color: #f8d7da;
        color: #721c24;
        padding: 0.5rem;
        border-radius: 0.25rem;
        font-weight: bold;
    }
    .sidebar-toggle {
        position: fixed;
        top: 80px;
        left: 10px;
        z-index: 999;
        background-color: #1f77b4;
        color: white;
        border: none;
        border-radius: 5px;
        padding: 8px 12px;
        cursor: pointer;
        font-size: 16px;
    }
    .sidebar-toggle:hover {
        background-color: #0d5aa7;
    }
    .hidden-sidebar {
        display: none !important;
    }
    .expanded-chart {
        width: 100% !important;
    }
</style>
""", unsafe_allow_html=True)

# Initialize session state for connection management
if 'websocket_engine' not in st.session_state:
    st.session_state.websocket_engine = None
if 'is_connected' not in st.session_state:
    st.session_state.is_connected = False
if 'last_update' not in st.session_state:
    st.session_state.last_update = None
if 'current_data' not in st.session_state:
    st.session_state.current_data = pd.DataFrame()

# Initialize session state for UI management
if 'sidebar_visible' not in st.session_state:
    st.session_state.sidebar_visible = True
if 'selected_indicators' not in st.session_state:
    st.session_state.selected_indicators = ['RSI', 'MACD', 'VWAP']
if 'chart_initialized' not in st.session_state:
    st.session_state.chart_initialized = False
if 'last_candle_count' not in st.session_state:
    st.session_state.last_candle_count = 0
if 'chart_data' not in st.session_state:
    st.session_state.chart_data = pd.DataFrame()
if 'current_ml_prediction' not in st.session_state:
    st.session_state.current_ml_prediction = None

# Initialize session state for connection caching (CRITICAL FOR PREVENTING RECONNECTIONS)
if 'kite_authenticated' not in st.session_state:
    st.session_state.kite_authenticated = False
if 'kite_connection_cached' not in st.session_state:
    st.session_state.kite_connection_cached = None
if 'auth_completed_time' not in st.session_state:
    st.session_state.auth_completed_time = None
if 'websocket_initialized' not in st.session_state:
    st.session_state.websocket_initialized = False
if 'websocket_connected_time' not in st.session_state:
    st.session_state.websocket_connected_time = None
if 'auto_refresh_active' not in st.session_state:
    st.session_state.auto_refresh_active = True  # Enable by default
if 'refresh_error_count' not in st.session_state:
    st.session_state.refresh_error_count = 0

# Initialize session state for auto-refresh optimization
if 'auto_refresh_active' not in st.session_state:
    st.session_state.auto_refresh_active = False
if 'last_data_refresh' not in st.session_state:
    st.session_state.last_data_refresh = None
if 'refresh_counter' not in st.session_state:
    st.session_state.refresh_counter = 0

def initialize_websocket():
    """Initialize WebSocket connection"""
    try:
        # Try to initialize with proper credentials
        engine = KiteTickerEngine()

        if engine.connect():
            st.session_state.websocket_engine = engine
            st.session_state.is_connected = True
            st.success("✅ Connected to live market data!")
            return True
        else:
            raise Exception("Failed to establish WebSocket connection")

    except Exception as e:
        st.warning(f"⚠️ Live connection failed: {e}")
        st.info("📊 Using simulated data for demonstration. To enable live data:")
        st.info("1. Authenticate with Kite using the login button above")
        st.info("2. Or run `python refresh_token.py` to get a fresh token")
        st.session_state.is_connected = False
        return False

def get_seamless_market_data(symbol, instrument_token, timeframe="5minute"):
    """Get seamless market data combining historical context with live updates"""

    # Check if we should use live data
    use_live = should_use_live_data()

    # Try WebSocket first if market is open and connected
    if use_live and st.session_state.is_connected and st.session_state.websocket_engine:
        try:
            # Get live candle data with timeframe conversion
            live_candles = st.session_state.websocket_engine.get_candles(
                instrument_token,
                lookback=100,  # More candles for better context
                timeframe=timeframe
            )

            if not live_candles.empty:
                # Get historical data for seamless continuation
                historical_candles = get_historical_context(symbol, timeframe)

                # Create seamless data flow
                if not historical_candles.empty:
                    # Ensure timestamp columns are consistent and timezone-naive
                    if 'timestamp' not in live_candles.columns:
                        live_candles['timestamp'] = pd.date_range(
                            start=datetime.now() - timedelta(minutes=len(live_candles)),
                            periods=len(live_candles),
                            freq='5min' if timeframe == '5minute' else '1min'
                        )

                    # Ensure both dataframes have timezone-naive timestamps
                    for df_name, df in [('historical', historical_candles), ('live', live_candles)]:
                        if 'timestamp' in df.columns:
                            df['timestamp'] = pd.to_datetime(df['timestamp'])
                            if df['timestamp'].dt.tz is not None:
                                df['timestamp'] = df['timestamp'].dt.tz_localize(None)

                    # Smart combination for seamless flow
                    # Take last 150 historical candles for good context
                    historical_subset = historical_candles.tail(150)

                    # Find the cutoff point to avoid overlap
                    if not historical_subset.empty and not live_candles.empty:
                        last_historical_time = historical_subset['timestamp'].max()
                        live_candles = live_candles[live_candles['timestamp'] > last_historical_time]

                    # Combine for seamless continuation
                    combined_candles = pd.concat([
                        historical_subset,
                        live_candles
                    ], ignore_index=True)

                    # Remove any remaining duplicates and sort
                    combined_candles = combined_candles.drop_duplicates(subset=['timestamp'], keep='last')
                    combined_candles = combined_candles.sort_values('timestamp').reset_index(drop=True)

                    print(f"✅ Seamless data: {len(historical_subset)} historical + {len(live_candles)} live = {len(combined_candles)} total")
                    candles = combined_candles
                else:
                    candles = live_candles
                    print(f"✅ Live data only: {len(live_candles)} candles")

                # Calculate indicators
                candles = calculate_indicators(candles)
                st.session_state.current_data = candles
                st.session_state.last_update = datetime.now()

                return candles
        except Exception as e:
            print(f"⚠️ WebSocket error: {e}")

    # Use historical data when market is closed or WebSocket unavailable
    return get_simulated_data(symbol, timeframe)

# Keep the original function for backward compatibility
def get_live_data(symbol, instrument_token, timeframe="5minute"):
    """Legacy function - redirects to seamless data function"""
    return get_seamless_market_data(symbol, instrument_token, timeframe)

def get_historical_context(symbol, timeframe="5minute"):
    """Get historical data from previous trading days for context"""
    try:
        # Check if we've already determined that authentication failed
        if st.session_state.get('kite_auth_failed', False):
            return pd.DataFrame()

        from kite_auth_helper import get_kite_connection
        kite = get_kite_connection()

        if kite:
            # Get instrument token
            from kite_websocket import get_instrument_token
            instrument_token = get_instrument_token(symbol)

            if instrument_token:
                # Get last 3 trading days for better context
                from datetime import date, timedelta
                import pandas as pd

                end_date = date.today()
                start_date = end_date - timedelta(days=7)  # Go back 7 days to ensure we get trading days

                try:
                    historical_data = kite.historical_data(
                        instrument_token=instrument_token,
                        from_date=start_date,
                        to_date=end_date,
                        interval=timeframe
                    )

                    if historical_data and len(historical_data) > 0:
                        df = pd.DataFrame(historical_data)
                        df['timestamp'] = df['date']
                        df = df.drop('date', axis=1)

                        # Take last 200 candles for good context
                        if len(df) > 200:
                            df = df.tail(200).reset_index(drop=True)

                        print(f"✅ Historical context: {len(df)} candles from {start_date} to {end_date}")
                        return df

                except Exception as e:
                    print(f"⚠️ Could not fetch historical context: {e}")

    except Exception as e:
        print(f"⚠️ Error getting historical context: {e}")

    return pd.DataFrame()

def get_simulated_data(symbol, timeframe="5minute"):
    """Get real historical data from previous trading day during market closed hours"""
    try:
        # Check if we've already determined that authentication failed
        if 'kite_auth_failed' not in st.session_state:
            st.session_state.kite_auth_failed = False

        # Only try authentication once per session
        if not st.session_state.kite_auth_failed:
            from kite_auth_helper import get_kite_connection
            kite = get_kite_connection()

            if not kite:
                st.session_state.kite_auth_failed = True
                print("📊 Kite authentication failed, using saved historical data")
        else:
            kite = None

        if kite and not st.session_state.kite_auth_failed:
            # Get previous trading day's data
            from datetime import date, timedelta
            import pandas as pd

            # Calculate previous trading day (skip weekends)
            today = date.today()
            days_back = 1
            prev_date = today - timedelta(days=days_back)

            # Skip weekends - go back to Friday if today is Monday/Sunday
            while prev_date.weekday() >= 5:  # Saturday=5, Sunday=6
                days_back += 1
                prev_date = today - timedelta(days=days_back)

            # Get instrument token
            instrument_map = {
                "NIFTY": "NSE:NIFTY 50",
                "BANKNIFTY": "NSE:NIFTY BANK",
                "FINNIFTY": "NSE:NIFTY FIN SERVICE"
            }

            if symbol in instrument_map:
                try:
                    # Get the actual instrument token (numeric) from instruments list
                    instruments = kite.instruments("NSE")

                    # Map symbol to instrument name
                    instrument_name_map = {
                        "NIFTY": "NIFTY 50",
                        "BANKNIFTY": "NIFTY BANK",
                        "FINNIFTY": "NIFTY FIN SERVICE"
                    }

                    instrument_token = None
                    target_name = instrument_name_map.get(symbol, symbol)

                    for inst in instruments:
                        if inst['name'] == target_name and inst['exchange'] == 'NSE':
                            instrument_token = inst['instrument_token']
                            break

                    if not instrument_token:
                        print(f"⚠️ Could not find instrument token for {symbol}")
                        raise Exception(f"Instrument token not found for {symbol}")

                    print(f"🔍 Got instrument token for {symbol}: {instrument_token}")

                    # Try to fetch historical data with selected timeframe first, then fallbacks
                    intervals_to_try = [timeframe, "5minute", "15minute", "day"]
                    # Remove duplicates while preserving order
                    intervals_to_try = list(dict.fromkeys(intervals_to_try))

                    for interval in intervals_to_try:
                        try:
                            print(f"📊 Trying to fetch {interval} data for {prev_date}...")

                            historical_data = kite.historical_data(
                                instrument_token=instrument_token,
                                from_date=prev_date,
                                to_date=prev_date,
                                interval=interval
                            )

                            if historical_data and len(historical_data) > 0:
                                # Convert to DataFrame
                                df = pd.DataFrame(historical_data)
                                df['timestamp'] = df['date']
                                df = df.drop('date', axis=1)

                                # For day interval, replicate to create minute-like data
                                if interval == "day" and len(df) > 0:
                                    day_data = df.iloc[0]
                                    minute_data = []
                                    for _ in range(50):
                                        minute_data.append({
                                            'timestamp': prev_date,
                                            'open': day_data['open'],
                                            'high': day_data['high'],
                                            'low': day_data['low'],
                                            'close': day_data['close'],
                                            'volume': day_data['volume'] // 50
                                        })
                                    df = pd.DataFrame(minute_data)
                                    print(f"✅ Using replicated day data from {prev_date} ({len(df)} candles)")
                                else:
                                    # Take last 50 candles for consistency
                                    if len(df) > 50:
                                        df = df.tail(50).reset_index(drop=True)
                                    print(f"✅ Using real historical data from {prev_date} ({len(df)} candles, {interval} interval)")

                                # ALWAYS calculate indicators before returning
                                df = calculate_indicators(df)
                                return df

                        except Exception as interval_error:
                            print(f"⚠️ Failed to fetch {interval} data: {interval_error}")
                            continue

                    print(f"⚠️ Could not fetch any historical data for {symbol} on {prev_date}")

                except Exception as api_error:
                    print(f"⚠️ API error getting instrument token: {api_error}")

    except Exception as e:
        print(f"⚠️ Could not fetch historical data: {e}")

    # Always try CSV fallback if API fails
    print("📊 Trying to load saved historical data...")
    try:
        import pandas as pd
        df = pd.read_csv("data/training_data_90days.csv")
        if len(df) > 50:
            df = df.tail(50).reset_index(drop=True)
            # Ensure timestamp column exists
            if 'timestamp' not in df.columns and 'date' in df.columns:
                df['timestamp'] = df['date']
            elif 'timestamp' not in df.columns:
                df['timestamp'] = pd.date_range(
                    start=datetime.now() - timedelta(minutes=len(df)),
                    periods=len(df),
                    freq='1min'
                )
            print(f"✅ Using saved historical data ({len(df)} candles)")
            # ALWAYS calculate indicators before returning
            df = calculate_indicators(df)
            return df
    except Exception as csv_error:
        print(f"⚠️ Could not load saved data: {csv_error}")
        print("📊 Falling back to simulated data")

    # Fallback to simulated data if historical data fetch fails
    import numpy as np

    # Base prices for different symbols
    base_prices = {
        "NIFTY": 22000,
        "BANKNIFTY": 48000,
        "FINNIFTY": 20000
    }

    base_price = base_prices.get(symbol, 22000)

    # Generate 50 candles of simulated data
    np.random.seed(int(datetime.now().timestamp()) % 1000)  # Semi-random seed

    # Create realistic price movement
    returns = np.random.normal(0, 0.002, 50)  # 0.2% volatility
    prices = base_price * np.exp(np.cumsum(returns))

    # Generate OHLC data
    dates = pd.date_range(
        start=datetime.now() - timedelta(minutes=50),
        periods=50,
        freq='1min'
    )

    high_prices = prices * (1 + np.random.uniform(0, 0.005, 50))
    low_prices = prices * (1 - np.random.uniform(0, 0.005, 50))
    open_prices = np.roll(prices, 1)
    open_prices[0] = prices[0]

    # Add some trend to make it more realistic
    trend = np.linspace(0, np.random.uniform(-0.01, 0.01), 50)
    prices = prices * (1 + trend)
    high_prices = high_prices * (1 + trend)
    low_prices = low_prices * (1 + trend)
    open_prices = open_prices * (1 + trend)

    candles = pd.DataFrame({
        'timestamp': dates,
        'open': open_prices,
        'high': high_prices,
        'low': low_prices,
        'close': prices,
        'volume': np.random.randint(10000, 100000, 50)
    })

    # Calculate indicators
    candles = calculate_indicators(candles)
    st.session_state.current_data = candles
    st.session_state.last_update = datetime.now()

    return candles

def get_incremental_data(symbol, instrument_token):
    """Get only new/updated candle data for incremental chart updates"""

    # Rate limiting: Don't make API calls too frequently
    now = datetime.now()
    if st.session_state.last_api_call:
        time_since_last_call = (now - st.session_state.last_api_call).total_seconds()
        if time_since_last_call < 5:  # Minimum 5 seconds between API calls
            return st.session_state.current_data, 'rate_limited'

    st.session_state.last_api_call = now
    st.session_state.api_call_count += 1

    # Check if we should use live data
    use_live = should_use_live_data()

    # Try WebSocket first if market is open and connected
    if use_live and st.session_state.is_connected and st.session_state.websocket_engine:
        try:
            # Get latest candle data
            candles = st.session_state.websocket_engine.get_candles(instrument_token, lookback=100)

            if not candles.empty:
                # Ensure required columns exist
                if 'timestamp' not in candles.columns and 'datetime' not in candles.columns:
                    candles['timestamp'] = pd.date_range(
                        start=datetime.now() - timedelta(minutes=len(candles)),
                        periods=len(candles),
                        freq='1min'
                    )

                # Calculate indicators
                candles = calculate_indicators(candles)

                # Check if we have new data
                current_count = len(candles)
                last_count = st.session_state.last_candle_count

                if current_count > last_count:
                    # New candle(s) added
                    st.session_state.last_candle_count = current_count
                    st.session_state.current_data = candles
                    st.session_state.last_update = datetime.now()
                    return candles, 'new_candle'
                elif current_count == last_count and not candles.empty:
                    # Same number of candles, check if last candle updated
                    if not st.session_state.current_data.empty:
                        old_last = st.session_state.current_data.iloc[-1]
                        new_last = candles.iloc[-1]

                        # Check if OHLC values changed
                        if (old_last['close'] != new_last['close'] or
                            old_last['high'] != new_last['high'] or
                            old_last['low'] != new_last['low'] or
                            old_last['volume'] != new_last['volume']):

                            st.session_state.current_data = candles
                            st.session_state.last_update = datetime.now()
                            return candles, 'update_candle'

                return candles, 'no_change'

        except Exception as e:
            st.warning(f"WebSocket error: {e}. Using simulated data for demonstration.")

    # Fallback to simulated data
    candles = get_simulated_data(symbol, "5minute")
    st.session_state.last_candle_count = len(candles)
    return candles, 'new_data'

def initialize_connections_once():
    """Initialize all connections ONCE per session - never re-authenticate during auto-refresh"""

    # Skip if already authenticated
    if st.session_state.kite_authenticated:
        return st.session_state.kite_connection_cached

    # First-time authentication only
    with st.spinner("🔐 Authenticating with Kite API (one-time setup)..."):
        kite_connection = handle_kite_auth_in_streamlit()

        if kite_connection:
            st.session_state.kite_authenticated = True
            st.session_state.kite_connection_cached = kite_connection
            st.session_state.auth_completed_time = datetime.now()

            # Only show success message for first 5 seconds after authentication
            time_since_auth = (datetime.now() - st.session_state.auth_completed_time).total_seconds()
            if time_since_auth < 5:
                st.success("✅ Authentication completed! This won't happen again during auto-refresh.")

            # Initialize WebSocket if not already done
            if not st.session_state.websocket_initialized:
                if initialize_websocket():
                    st.session_state.websocket_initialized = True
                    st.session_state.websocket_connected_time = datetime.now()

                    # Only show WebSocket success message for first 5 seconds
                    time_since_ws = (datetime.now() - st.session_state.websocket_connected_time).total_seconds()
                    if time_since_ws < 5:
                        st.success("✅ WebSocket connected! Ready for real-time updates.")

        return kite_connection

def get_websocket_data_only(symbol, instrument_token, timeframe="5minute"):
    """Get data from WebSocket ONLY - no API calls, no reconnections"""

    # Skip if not connected
    if not st.session_state.is_connected or not st.session_state.websocket_engine:
        return get_simulated_data(symbol, timeframe), 'demo_mode'

    try:
        # Get data from existing WebSocket connection (NO API CALLS)
        candles = st.session_state.websocket_engine.get_candles(instrument_token, lookback=100, timeframe=timeframe)

        if not candles.empty:
            # Calculate indicators
            candles = calculate_indicators(candles)

            # Check for updates
            current_count = len(candles)
            last_count = st.session_state.last_candle_count

            if current_count > last_count:
                st.session_state.last_candle_count = current_count
                st.session_state.current_data = candles
                st.session_state.last_update = datetime.now()
                return candles, 'new_candle'
            elif current_count == last_count and not candles.empty:
                if not st.session_state.current_data.empty:
                    old_last = st.session_state.current_data.iloc[-1]
                    new_last = candles.iloc[-1]

                    if (old_last['close'] != new_last['close'] or
                        old_last['high'] != new_last['high'] or
                        old_last['low'] != new_last['low']):

                        st.session_state.current_data = candles
                        st.session_state.last_update = datetime.now()
                        return candles, 'update_candle'

            return candles, 'no_change'

    except Exception as e:
        st.warning(f"WebSocket data error: {e}")
        return get_simulated_data(symbol, "5minute"), 'websocket_error'

def main():
    """Main dashboard function with optimized auto-refresh (no reconnections)"""

    # Minimal header
    st.markdown('<h1 class="main-header">� Live Trading</h1>', unsafe_allow_html=True)

    # Sidebar toggle button
    col_toggle, col_spacer = st.columns([1, 10])
    with col_toggle:
        if st.button("☰" if st.session_state.sidebar_visible else "☰", key="sidebar_toggle"):
            st.session_state.sidebar_visible = not st.session_state.sidebar_visible
            st.rerun()

    # ONE-TIME CONNECTION SETUP (never repeats during auto-refresh)
    if not st.session_state.kite_authenticated:
        st.info("🔐 **Setting up connections...** This happens only once per session.")
        kite_connection = initialize_connections_once()
    else:
        # Use cached connection (no API calls)
        kite_connection = st.session_state.kite_connection_cached

        # Show connection status without API calls
        if st.session_state.auth_completed_time:
            time_since_auth = (datetime.now() - st.session_state.auth_completed_time).total_seconds()
            st.success(f"✅ **Connected to Kite API** (authenticated {int(time_since_auth/60)} minutes ago)")

    # Connection status banner - only show during initial setup or if there are issues
    show_connection_banner = False

    if not st.session_state.is_connected and not st.session_state.kite_authenticated:
        # Show demo mode message
        st.info("📊 **Demo mode** - Using simulated data for demonstration")
        show_connection_banner = True
    elif st.session_state.kite_authenticated and not st.session_state.is_connected:
        # Show WebSocket pending only if it's been more than 10 seconds since auth
        if hasattr(st.session_state, 'auth_completed_time'):
            time_since_auth = (datetime.now() - st.session_state.auth_completed_time).total_seconds()
            if time_since_auth > 10:
                st.warning("🔌 **WebSocket pending** - API authenticated, WebSocket initializing")
                show_connection_banner = True
        else:
            st.warning("🔌 **WebSocket pending** - API authenticated, WebSocket initializing")
            show_connection_banner = True

    # Only show success message briefly after connection
    if st.session_state.is_connected and hasattr(st.session_state, 'websocket_connected_time'):
        time_since_ws = (datetime.now() - st.session_state.websocket_connected_time).total_seconds()
        if time_since_ws < 10:  # Show for 10 seconds after connection
            st.success("🔌 **WebSocket connected** - Real-time data active")
            show_connection_banner = True

    # Conditional Sidebar Controls
    if st.session_state.sidebar_visible:
        with st.sidebar:
            st.header("🎛️ Controls")

            # Symbol selection with auto-refresh on change
            symbol = st.selectbox(
                "📊 Select Instrument",
                ["NIFTY", "BANKNIFTY", "FINNIFTY"],
                index=0,
                key="symbol_selector"
            )

            # Timeframe selection
            timeframe = st.selectbox(
                "⏱️ Select Timeframe",
                ["1minute", "5minute", "15minute"],
                index=1,  # Default to 5minute
                key="timeframe_selector"
            )

            # Reset chart when symbol or timeframe changes
            if 'previous_symbol' not in st.session_state:
                st.session_state.previous_symbol = symbol
                st.session_state.previous_timeframe = timeframe
            elif st.session_state.previous_symbol != symbol or st.session_state.get('previous_timeframe') != timeframe:
                st.session_state.chart_initialized = False
                st.session_state.current_data = pd.DataFrame()
                st.session_state.previous_symbol = symbol
                st.session_state.previous_timeframe = timeframe
                st.rerun()

            # Optimized auto-refresh settings (enabled by default for better UX)
            auto_refresh = st.checkbox("🔄 Real-time Updates", value=True)
            if auto_refresh:
                refresh_interval = st.slider("Update Interval (seconds)", 2, 10, 3)
                st.caption("⚡ **WebSocket-only updates** - No API reconnections!")
                st.success("✅ Safe for continuous use - uses existing connections only")
                st.session_state.auto_refresh_active = True

                # Add safety controls
                col_safety1, col_safety2 = st.columns(2)
                with col_safety1:
                    if st.button("⏸️ Pause", key="pause_refresh"):
                        st.session_state.auto_refresh_active = False
                        st.rerun()
                with col_safety2:
                    if st.button("🔄 Force Refresh", key="force_refresh"):
                        st.session_state.chart_initialized = False
                        st.rerun()
            else:
                refresh_interval = 5
                st.caption("📊 Manual mode: Click refresh to update chart")
                st.session_state.auto_refresh_active = False

                if st.button("▶️ Start Auto-Refresh", key="start_refresh"):
                    st.session_state.auto_refresh_active = True
                    st.rerun()

            # Indicator selection
            st.subheader("📊 Chart Indicators")
            available_indicators = ['RSI', 'MACD', 'VWAP', 'EMA_12', 'EMA_26', 'SMA_20', 'SMA_50', 'BB_upper', 'BB_lower', 'Stoch_K', 'Stoch_D']
            st.session_state.selected_indicators = st.multiselect(
                "Select indicators to display:",
                available_indicators,
                default=st.session_state.selected_indicators
            )

            # Market Status
            st.subheader("🕒 Market Status")
            market_status = get_market_status_display()

            if market_status['color'] == 'green':
                st.success(f"{market_status['emoji']} {market_status['text']}")
            else:
                st.error(f"{market_status['emoji']} {market_status['text']}")
            st.caption(market_status['details'])

            # Connection status (no API calls during auto-refresh)
            st.subheader("🔌 Connection Status")

            if st.session_state.is_connected:
                st.success("✅ **Live WebSocket Connected**")
                if not should_use_live_data():
                    st.warning("⚠️ Market closed - data may be stale")
                st.caption("Real-time data flowing from WebSocket")
            elif st.session_state.kite_authenticated:
                st.warning("🔄 **API Connected, WebSocket Pending**")
                if st.button("🔗 Connect WebSocket"):
                    with st.spinner("Connecting..."):
                        if initialize_websocket():
                            st.session_state.websocket_initialized = True
                            st.rerun()
            else:
                st.warning("📊 **Demo Mode** (Simulated Data)")
                st.info("Click 'Authenticate' above for live data")
                if st.button("🔐 Authenticate Now"):
                    st.rerun()  # This will trigger one-time authentication

            # Last update time and chart status
            if st.session_state.last_update:
                st.info(f"🕒 Last Update: {st.session_state.last_update.strftime('%H:%M:%S')}")

            if st.session_state.chart_initialized:
                st.success("📈 Chart: Real-time Mode")
                st.caption(f"📊 Showing {len(st.session_state.chart_data)} candles")
            else:
                st.warning("📈 Chart: Initializing...")

            # Connection monitoring (optimized)
            st.subheader("📊 Connection Monitor")

            col_auth, col_ws = st.columns(2)
            with col_auth:
                if st.session_state.kite_authenticated:
                    st.metric("🔐 API Auth", "✅ Connected")
                else:
                    st.metric("🔐 API Auth", "❌ Not Connected")

            with col_ws:
                if st.session_state.is_connected:
                    st.metric("🔌 WebSocket", "✅ Live")
                else:
                    st.metric("🔌 WebSocket", "❌ Offline")

            # Auto-refresh monitoring with health status
            if st.session_state.auto_refresh_active:
                col_refresh, col_health = st.columns(2)
                with col_refresh:
                    st.metric("🔄 Auto-refresh", f"#{st.session_state.refresh_counter}")
                with col_health:
                    error_count = st.session_state.get('refresh_error_count', 0)
                    if error_count == 0:
                        st.metric("💚 Health", "Perfect")
                    elif error_count <= 2:
                        st.metric("💛 Health", f"{error_count} errors")
                    else:
                        st.metric("❤️ Health", f"{error_count} errors")

                if st.session_state.last_data_refresh:
                    st.caption(f"Last: {st.session_state.last_data_refresh.strftime('%H:%M:%S')}")
                st.success("✅ **No API reconnections** during auto-refresh!")
            else:
                st.metric("🔄 Auto-refresh", "Disabled")
                st.caption("Enable above for real-time updates")

            # Model Performance Section
            st.subheader("🤖 ML Model Status")
            try:
                from model_performance import get_performance_metrics, get_model_recommendations

                metrics = get_performance_metrics()

                # Health score with color coding
                if metrics['status_color'] == 'success':
                    st.success(f"🎯 Model Health: {metrics['health_score']}/100 ({metrics['status']})")
                elif metrics['status_color'] == 'warning':
                    st.warning(f"🎯 Model Health: {metrics['health_score']}/100 ({metrics['status']})")
                else:
                    st.error(f"🎯 Model Health: {metrics['health_score']}/100 ({metrics['status']})")

                # Key metrics
                col_acc, col_samples = st.columns(2)
                with col_acc:
                    st.metric("🎯 Best Accuracy", f"{metrics['best_accuracy']:.1f}%")
                with col_samples:
                    st.metric("📊 Training Data", f"{metrics['training_samples']:,}")

                # Top recommendation
                recommendations = get_model_recommendations()
                if recommendations:
                    top_rec = recommendations[0]
                    if top_rec['type'] == 'SUCCESS':
                        st.success(f"✅ {top_rec['title']}")
                    elif top_rec['type'] == 'WARNING':
                        st.warning(f"⚠️ {top_rec['title']}")
                    else:
                        st.error(f"❌ {top_rec['title']}")
                    st.caption(top_rec['description'])

            except Exception as e:
                st.error(f"Error loading model metrics: {e}")
    else:
        # Default values when sidebar is hidden - optimized for auto-refresh
        symbol = "NIFTY"
        timeframe = "5minute"
        auto_refresh = True
        refresh_interval = 3

    # Get instrument token
    try:
        instrument_token = get_instrument_token(symbol)

        # Subscribe to live data if connected
        if st.session_state.is_connected and st.session_state.websocket_engine:
            st.session_state.websocket_engine.subscribe([instrument_token])
    except Exception as e:
        st.error(f"Error getting instrument token: {e}")
        instrument_token = None

    # Main layout - Chart and Signals
    if st.session_state.sidebar_visible:
        # With sidebar: Chart and Signals side by side
        col_chart, col_signals = st.columns([3, 1])
    else:
        # Without sidebar: Chart takes more space, signals on the right
        col_chart, col_signals = st.columns([4, 1])

    # Chart Column - Clean and minimal
    with col_chart:
        # No subheader for cleaner look

        chart_placeholder = st.empty()

        # Initialize chart or handle manual refresh
        if instrument_token and (not st.session_state.chart_initialized or st.button("🔄 Refresh Chart")):
            with st.spinner("Loading market data..."):
                # Get seamless market data with historical context
                candles = get_seamless_market_data(symbol, instrument_token, timeframe)

                if candles is not None and not candles.empty:
                    # Get ML prediction for chart integration
                    ml_prediction = predict_ce_pe(candles)

                    # Detect patterns and support/resistance
                    patterns = detect_patterns(candles)
                    support_levels, resistance_levels = calculate_support_resistance(candles)

                    # Create Zerodha-style chart with ML predictions
                    fig = create_zerodha_style_chart(
                        candles.tail(100),  # Show more candles for better context
                        patterns,
                        support_levels,
                        resistance_levels,
                        ml_prediction=ml_prediction,
                        show_indicators=len(st.session_state.selected_indicators) > 0
                    )

                    chart_placeholder.plotly_chart(fig, use_container_width=True)
                    st.session_state.chart_initialized = True
                    st.session_state.chart_data = candles.tail(100)
                    st.session_state.current_ml_prediction = ml_prediction
                else:
                    chart_placeholder.plotly_chart(create_live_chart_placeholder(), use_container_width=True)

        # OPTIMIZED: Real-time updates using WebSocket only (no API calls)
        elif instrument_token and auto_refresh and st.session_state.chart_initialized:
            st.session_state.refresh_counter += 1

            # Get data from WebSocket only (no API reconnections)
            candles, update_type = get_websocket_data_only(symbol, instrument_token, timeframe)

            if update_type in ['new_candle', 'update_candle'] and candles is not None and not candles.empty:
                # Get updated ML prediction
                ml_prediction = predict_ce_pe(candles)

                # Only update if there's actual change
                patterns = detect_patterns(candles)
                support_levels, resistance_levels = calculate_support_resistance(candles)

                # Create updated Zerodha-style chart
                fig = create_zerodha_style_chart(
                    candles.tail(100),
                    patterns,
                    support_levels,
                    resistance_levels,
                    ml_prediction=ml_prediction,
                    show_indicators=len(st.session_state.selected_indicators) > 0
                )

                chart_placeholder.plotly_chart(fig, use_container_width=True)
                st.session_state.chart_data = candles.tail(100)
                st.session_state.current_ml_prediction = ml_prediction
                st.session_state.last_data_refresh = datetime.now()

                # Show minimal update indicator
                if update_type == 'new_candle':
                    st.success(f"🕯️ New candle #{st.session_state.refresh_counter}")
                elif update_type == 'update_candle':
                    st.info(f"📊 Live update #{st.session_state.refresh_counter}")
            elif update_type == 'no_change':
                st.info(f"📊 No new data (refresh #{st.session_state.refresh_counter}) - WebSocket active")
            elif update_type == 'demo_mode':
                st.warning(f"📊 Demo mode data (refresh #{st.session_state.refresh_counter})")

        # Show placeholder if no data
        elif not st.session_state.chart_initialized:
            chart_placeholder.plotly_chart(create_live_chart_placeholder(), use_container_width=True)

    # Signals Column - Minimal like your preference
    with col_signals:
        st.subheader("🎯 ML Signal")

        # Use cached prediction if available
        prediction = st.session_state.current_ml_prediction

        if prediction is None and not st.session_state.current_data.empty:
            # Get ML prediction only if not cached
            prediction = predict_ce_pe(st.session_state.current_data)
            st.session_state.current_ml_prediction = prediction

        if prediction:
            # Display signal prominently
            signal_class = "signal-bullish" if "CE" in prediction['signal'] else "signal-bearish"
            st.markdown(f'<div class="{signal_class}">🎯 {prediction["signal"]}</div>', unsafe_allow_html=True)

            # Key metrics only
            col_conf, col_price = st.columns(2)
            with col_conf:
                st.metric("Confidence", f"{prediction['confidence']:.0f}%")
            with col_price:
                current_price = prediction.get('current_price', 0)
                st.metric("Price", f"₹{current_price:.0f}")

            col_sl, col_target = st.columns(2)
            with col_sl:
                st.metric("Stop Loss", f"₹{prediction['stoploss']:.0f}")
            with col_target:
                st.metric("Target", f"₹{prediction['target']:.0f}")

            # Minimal trend info
            trend = prediction.get('trend', 'Unknown')
            trend_color = "🟢" if trend == "Bullish" else "🔴" if trend == "Bearish" else "🟡"
            st.write(f"{trend_color} **{trend}**")

        else:
            st.info("⏳ Analyzing...")

    # Market Info Section - Below Chart
    st.markdown("---")
    st.subheader("📊 Market Information & Status")

    # Market status and data source info
    market_status = get_market_status_display()
    data_source = get_data_source_info()

    col1, col2, col3 = st.columns(3)

    with col1:
        if market_status['color'] == 'green':
            st.success(f"{market_status['emoji']} **{market_status['text']}**")
        else:
            st.warning(f"{market_status['emoji']} **{market_status['text']}**")
        st.caption(market_status['details'])

    with col2:
        if data_source['source'] == 'Live Market Data':
            st.info(f"📡 **{data_source['source']}**")
        else:
            st.info(f"📊 **{data_source['source']}**")
        st.caption(data_source['description'])

    with col3:
        # Display OHLC and current price info
        if not st.session_state.current_data.empty:
            latest = st.session_state.current_data.iloc[-1]

            # Try to get live accurate price for comparison
            try:
                if st.session_state.kite_authenticated and st.session_state.kite_connection_cached:
                    kite = st.session_state.kite_connection_cached
                    instrument_map = {
                        "NIFTY": "NSE:NIFTY 50",
                        "BANKNIFTY": "NSE:NIFTY BANK",
                        "FINNIFTY": "NSE:NIFTY FIN SERVICE"
                    }

                    if symbol in instrument_map:
                        live_quote = kite.ltp(instrument_map[symbol])
                        live_price = list(live_quote.values())[0]['last_price']
                        chart_price = latest['close']
                        price_diff = abs(live_price - chart_price)

                        if price_diff > 5:  # If difference > 5 points
                            st.warning(f"⚠️ Price Difference: Live ₹{live_price:.2f} vs Chart ₹{chart_price:.2f} (Δ{price_diff:.1f})")
                        else:
                            st.success(f"✅ Live Price: ₹{live_price:.2f} (Accurate)")
                    else:
                        st.caption("📊 Using chart data")
                else:
                    st.caption("📊 Using historical/chart data")
            except Exception as e:
                st.caption(f"📊 Chart data (Live check failed)")

            # OHLC Display
            st.subheader("📊 OHLC Data")
            col_o, col_h = st.columns(2)
            with col_o:
                st.metric("� Open", f"₹{latest['open']:.2f}")
            with col_h:
                st.metric("🔺 High", f"₹{latest['high']:.2f}")

            col_l, col_c = st.columns(2)
            with col_l:
                st.metric("🔻 Low", f"₹{latest['low']:.2f}")
            with col_c:
                # Calculate change from open
                change = latest['close'] - latest['open']
                change_pct = (change / latest['open']) * 100
                delta_color = "normal" if change >= 0 else "inverse"
                st.metric(
                    "💰 Close",
                    f"₹{latest['close']:.2f}",
                    delta=f"{change:+.2f} ({change_pct:+.2f}%)",
                    delta_color=delta_color
                )

    # Technical Indicators Section
    st.markdown("---")
    st.subheader("📈 Technical Indicators")

    if not st.session_state.current_data.empty:
        latest = st.session_state.current_data.iloc[-1]

        # Technical indicators in a compact format
        col_rsi, col_macd, col_vwap = st.columns(3)
        with col_rsi:
            rsi_value = latest.get('RSI', 0)
            rsi_color = "normal" if 30 <= rsi_value <= 70 else "inverse"
            st.metric("RSI", f"{rsi_value:.1f}", delta_color=rsi_color)
        with col_macd:
            st.metric("MACD", f"{latest.get('MACD', 0):.3f}")
        with col_vwap:
            st.metric("VWAP", f"₹{latest.get('VWAP', 0):.2f}")

        # Additional indicators
        col_ema, col_sma, col_vol = st.columns(3)
        with col_ema:
            st.metric("EMA(12)", f"₹{latest.get('EMA_12', 0):.2f}")
        with col_sma:
            st.metric("SMA(20)", f"₹{latest.get('SMA_20', 0):.2f}")
        with col_vol:
            volume = latest.get('volume', 0)
            if volume > 1000000:
                vol_display = f"{volume/1000000:.1f}M"
            elif volume > 1000:
                vol_display = f"{volume/1000:.1f}K"
            else:
                vol_display = f"{volume:.0f}"
            st.metric("Volume", vol_display)

    # Additional info for closed market
    if not should_use_live_data():
        st.info("📊 **Market is closed.** Showing real historical data from previous trading day for analysis.")

    # OPTIMIZED AUTO-REFRESH: WebSocket-only updates with error handling
    if auto_refresh and st.session_state.auto_refresh_active:
        try:
            # Add refresh counter and error tracking
            if 'refresh_error_count' not in st.session_state:
                st.session_state.refresh_error_count = 0

            # Safety check: pause if too many errors
            if st.session_state.refresh_error_count > 5:
                st.error("⚠️ Too many refresh errors. Auto-refresh paused for safety.")
                st.session_state.auto_refresh_active = False
                if st.button("🔄 Reset and Resume"):
                    st.session_state.refresh_error_count = 0
                    st.session_state.auto_refresh_active = True
                    st.rerun()
            else:
                # Much faster refresh since we're not making API calls
                time.sleep(refresh_interval)  # Can be as low as 2 seconds safely
                st.rerun()  # Only triggers data updates, not authentication

        except Exception as e:
            st.session_state.refresh_error_count += 1
            st.error(f"Auto-refresh error #{st.session_state.refresh_error_count}: {e}")
            if st.session_state.refresh_error_count <= 3:
                time.sleep(refresh_interval * 2)  # Slower retry
                st.rerun()

if __name__ == "__main__":
    main()
